[2025-08-12 00:15:30] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":14,"session_id":"ws_iNC6GDsABmIoeMpEG2bGOhriQi2pShNd"} 
[2025-08-12 00:15:31] production.INFO: WebSocket连接认证成功 {"session_id":"ws_iNC6GDsABmIoeMpEG2bGOhriQi2pShNd","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-12 00:15:46] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":14,"session_id":"ws_TETxAXo0xzPHQg1e2WMapP0wyCoe846D"} 
[2025-08-12 00:15:47] production.INFO: WebSocket连接认证成功 {"session_id":"ws_TETxAXo0xzPHQg1e2WMapP0wyCoe846D","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-12 00:17:50] production.ERROR: 创建文本生成任务失败 {"method":"App\\Http\\Controllers\\PyApi\\AiGenerationController::generateTextWithWebSocket","user_id":null,"error_context":{"prompt":"请写一个关于人工智能的简短介绍","model_id":"1","project_id":"1","max_tokens":"1000","temperature":"0.7","context":"测试WebSocket文本生成功能","websocket_session_id":"ws_TETxAXo0xzPHQg1e2WMapP0wyCoe846D"},"error":"指定的项目不存在","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php(285): App\\Http\\Controllers\\Controller->validateData(Array, Array, Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\AiGenerationController->generateTextWithWebSocket(Object(Laravel\\Lumen\\Http\\Request))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\AiGenerationController), 'generateTextWit...', Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(27): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#21 {main}"} 
[2025-08-12 00:22:00] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":14,"session_id":"ws_9lCDaxiJUxZxiy6zCamCp76kUqaGMsX0"} 
[2025-08-12 00:22:01] production.INFO: WebSocket连接认证成功 {"session_id":"ws_9lCDaxiJUxZxiy6zCamCp76kUqaGMsX0","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-12 00:22:12] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":14,"session_id":"ws_mUoRwg5bUevE7Ur7tcm2FV0sgzOYeEYk"} 
[2025-08-12 00:22:12] production.INFO: WebSocket连接认证成功 {"session_id":"ws_mUoRwg5bUevE7Ur7tcm2FV0sgzOYeEYk","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-12 00:25:06] production.INFO: 文本生成任务创建成功 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"context":"测试WebSocket文本生成功能","model_id":"1","project_id":null} 
[2025-08-12 00:27:27] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":3,"session_id":"ws_AJf7pvrFgov2jAG9RZl3ZBDucJ3Gyvz2"} 
[2025-08-12 00:27:27] production.INFO: WebSocket连接认证成功 {"session_id":"ws_AJf7pvrFgov2jAG9RZl3ZBDucJ3Gyvz2","user_id":3,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-12 00:30:18] production.ERROR: 创建文本生成任务失败 {"method":"App\\Http\\Controllers\\PyApi\\AiGenerationController::generateTextWithWebSocket","user_id":null,"error_context":{"prompt":"78687","model_id":"1","project_id":"1","max_tokens":"700","temperature":"0.6","websocket_session_id":"ws_AJf7pvrFgov2jAG9RZl3ZBDucJ3Gyvz2"},"error":"指定的项目不存在","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php(285): App\\Http\\Controllers\\Controller->validateData(Array, Array, Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\AiGenerationController->generateTextWithWebSocket(Object(Laravel\\Lumen\\Http\\Request))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\AiGenerationController), 'generateTextWit...', Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(27): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#21 {main}"} 
[2025-08-12 00:30:41] production.ERROR: 创建文本生成任务失败 {"method":"App\\Http\\Controllers\\PyApi\\AiGenerationController::generateTextWithWebSocket","user_id":null,"error_context":{"prompt":"78687","model_id":"1","project_id":"2","max_tokens":"700","temperature":"0.6","websocket_session_id":"ws_AJf7pvrFgov2jAG9RZl3ZBDucJ3Gyvz2"},"error":"指定的项目不存在","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php(285): App\\Http\\Controllers\\Controller->validateData(Array, Array, Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\AiGenerationController->generateTextWithWebSocket(Object(Laravel\\Lumen\\Http\\Request))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\AiGenerationController), 'generateTextWit...', Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(27): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#21 {main}"} 
[2025-08-12 00:31:32] production.ERROR: 创建文本生成任务失败 {"method":"App\\Http\\Controllers\\PyApi\\AiGenerationController::generateTextWithWebSocket","user_id":null,"error_context":{"prompt":"78687","model_id":"1","project_id":"1","max_tokens":"700","temperature":"0.6","websocket_session_id":"ws_AJf7pvrFgov2jAG9RZl3ZBDucJ3Gyvz2"},"error":"指定的项目不存在","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php(285): App\\Http\\Controllers\\Controller->validateData(Array, Array, Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\AiGenerationController->generateTextWithWebSocket(Object(Laravel\\Lumen\\Http\\Request))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\AiGenerationController), 'generateTextWit...', Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(27): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#21 {main}"} 
[2025-08-12 00:31:50] production.ERROR: 创建文本生成任务失败 {"method":"App\\Http\\Controllers\\PyApi\\AiGenerationController::generateTextWithWebSocket","user_id":null,"error_context":{"prompt":"78687","model_id":"1","project_id":"1","max_tokens":"700","temperature":"0.6","websocket_session_id":"ws_AJf7pvrFgov2jAG9RZl3ZBDucJ3Gyvz2"},"error":"指定的项目不存在","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php(285): App\\Http\\Controllers\\Controller->validateData(Array, Array, Array, Array)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Http\\Controllers\\PyApi\\AiGenerationController->generateTextWithWebSocket(Object(Laravel\\Lumen\\Http\\Request))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(391): Illuminate\\Container\\Container->call(Array, Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(357): Laravel\\Lumen\\Application->callControllerCallable(Array, Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(331): Laravel\\Lumen\\Application->callLumenController(Object(App\\Http\\Controllers\\PyApi\\AiGenerationController), 'generateTextWit...', Array)
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(284): Laravel\\Lumen\\Application->callControllerAction(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(269): Laravel\\Lumen\\Application->callActionOnArrayBasedRoute(Array)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(171): Laravel\\Lumen\\Application->handleFoundRoute(Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(48): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Middleware\\CrossOriginMiddleware.php(27): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CrossOriginMiddleware->handle(Object(Laravel\\Lumen\\Http\\Request), Object(Closure))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Routing\\Pipeline.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Laravel\\Lumen\\Routing\\Pipeline->Laravel\\Lumen\\Routing\\{closure}(Object(Laravel\\Lumen\\Http\\Request))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(428): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(177): Laravel\\Lumen\\Application->sendThroughPipeline(Array, Object(Closure))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RoutesRequests.php(112): Laravel\\Lumen\\Application->dispatch(NULL)
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(28): Laravel\\Lumen\\Application->run()
#21 {main}"} 
[2025-08-12 00:45:48] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":14,"session_id":"ws_RoVwuPcrO7jQGwFtpW44G5s2QKdWgr5v"} 
[2025-08-12 00:45:48] production.INFO: WebSocket连接认证成功 {"session_id":"ws_RoVwuPcrO7jQGwFtpW44G5s2QKdWgr5v","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-12 00:48:55] production.INFO: 文本生成任务创建成功 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"context":"测试WebSocket文本生成功能","model_id":"1","project_id":null} 
[2025-08-12 00:56:46] production.ERROR: There are no commands defined in the "config" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"config\" namespace. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:669)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(720): Symfony\\Component\\Console\\Application->findNamespace('config')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('config:clear')
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2025-08-12 00:59:55] production.ERROR: Call to a member function connection() on null {"exception":"[object] (Error(code: 0): Call to a member function connection() on null at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php:1819)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1785): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1576): Illuminate\\Database\\Eloquent\\Model->getConnection()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1495): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1531): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1484): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->newQuery()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('where', Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php(32): Illuminate\\Database\\Eloquent\\Model::__callStatic('where', Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\test_websocket_call.php(18): App\\Services\\PyApi\\WebSocketEventService->pushAiGenerationProgress('test_task_123', 1, 50, 'Test message')
#9 {main}
"} 
[2025-08-12 01:00:22] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress"} 
[2025-08-12 01:00:22] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13} 
[2025-08-12 01:00:22] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress"} 
[2025-08-12 01:00:22] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13} 
[2025-08-12 01:00:22] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13} 
[2025-08-12 01:00:22] production.INFO: 积分冻结成功 {"user_id":13,"amount":1.4,"business_type":"text_generation","business_id":null,"transaction_id":10,"freeze_id":10} 
[2025-08-12 01:00:22] production.INFO: AiGenerationService创建任务 {"user_id":13,"business_type_from_websocket":null,"final_task_type":"text_generation","model_id":null,"project_id":null} 
[2025-08-12 01:00:22] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":115} 
[2025-08-12 01:00:22] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-12 01:00:22] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-12 01:00:22] production.INFO: 用户偏好记录更新成功 {"user_id":13,"platform":"deepseek","task_type":"text_generation","usage_count":5,"success_rate":0.8} 
[2025-08-12 01:00:22] production.INFO: 积分消费成功 {"transaction_id":10,"user_id":13,"amount":"1.40"} 
[2025-08-12 01:00:22] production.INFO: 文本生成任务完成 {"task_id":10,"platform":"deepseek","mode":"mock","tokens_used":0,"processing_time":258,"has_project":false} 
[2025-08-12 01:00:22] production.INFO: 文本生成任务创建成功 {"task_id":10,"user_id":13,"model_id":1,"estimated_cost":1.4} 
[2025-08-12 01:00:22] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress"} 
[2025-08-12 01:00:22] production.INFO: AI生成进度推送 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13,"progress":80,"success_count":0} 
[2025-08-12 01:00:22] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-12 01:00:22] production.INFO: AI生成进度推送 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13,"progress":100,"success_count":0} 
[2025-08-12 01:00:22] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13,"push_type":"ai_generation_completed"} 
[2025-08-12 01:00:22] production.INFO: AI生成完成推送 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13,"task_type":"text_generation","success_count":0} 
[2025-08-12 01:00:22] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1754893952_1RTfSl8G","user_id":13,"context":"prompt_edit","ai_task_id":10,"cost":0} 
[2025-08-12 01:03:21] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress"} 
[2025-08-12 01:03:21] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13} 
[2025-08-12 01:03:21] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress"} 
[2025-08-12 01:03:21] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13} 
[2025-08-12 01:03:21] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13} 
[2025-08-12 01:03:21] production.INFO: 积分冻结成功 {"user_id":13,"amount":1.4,"business_type":"text_generation","business_id":null,"transaction_id":11,"freeze_id":11} 
[2025-08-12 01:03:21] production.INFO: AiGenerationService创建任务 {"user_id":13,"business_type_from_websocket":null,"final_task_type":"text_generation","model_id":null,"project_id":null} 
[2025-08-12 01:03:21] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":115} 
[2025-08-12 01:03:21] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-12 01:03:22] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-12 01:03:22] production.INFO: 用户偏好记录更新成功 {"user_id":13,"platform":"deepseek","task_type":"text_generation","usage_count":6,"success_rate":0.8333333333333334} 
[2025-08-12 01:03:22] production.INFO: 积分消费成功 {"transaction_id":11,"user_id":13,"amount":"1.40"} 
[2025-08-12 01:03:22] production.INFO: 文本生成任务完成 {"task_id":11,"platform":"deepseek","mode":"mock","tokens_used":0,"processing_time":1060,"has_project":false} 
[2025-08-12 01:03:22] production.INFO: 文本生成任务创建成功 {"task_id":11,"user_id":13,"model_id":1,"estimated_cost":1.4} 
[2025-08-12 01:03:22] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress"} 
[2025-08-12 01:03:22] production.INFO: AI生成进度推送 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13,"progress":80,"success_count":0} 
[2025-08-12 01:03:22] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-12 01:03:22] production.INFO: AI生成进度推送 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13,"progress":100,"success_count":0} 
[2025-08-12 01:03:22] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13,"push_type":"ai_generation_completed"} 
[2025-08-12 01:03:22] production.INFO: AI生成完成推送 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13,"task_type":"text_generation","success_count":0} 
[2025-08-12 01:03:22] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1754896323_Cp7hDBN0","user_id":13,"context":"prompt_edit","ai_task_id":11,"cost":0} 
[2025-08-12 01:04:14] production.ERROR: Command "tinker" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"tinker\" is not defined. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('tinker')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-08-12 01:05:44] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress"} 
[2025-08-12 01:05:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13} 
[2025-08-12 01:05:44] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress"} 
[2025-08-12 01:05:44] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13} 
[2025-08-12 01:05:44] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13} 
[2025-08-12 01:05:44] production.INFO: 积分冻结成功 {"user_id":13,"amount":1.4,"business_type":"text_generation","business_id":null,"transaction_id":12,"freeze_id":12} 
[2025-08-12 01:05:44] production.INFO: AiGenerationService创建任务 {"user_id":13,"business_type_from_websocket":null,"final_task_type":"text_generation","model_id":null,"project_id":null} 
[2025-08-12 01:05:44] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":115} 
[2025-08-12 01:05:44] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-12 01:05:44] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":0} 
[2025-08-12 01:05:44] production.INFO: 用户偏好记录更新成功 {"user_id":13,"platform":"deepseek","task_type":"text_generation","usage_count":7,"success_rate":0.8571428571428571} 
[2025-08-12 01:05:44] production.INFO: 积分消费成功 {"transaction_id":12,"user_id":13,"amount":"1.40"} 
[2025-08-12 01:05:44] production.INFO: 文本生成任务完成 {"task_id":12,"platform":"deepseek","mode":"mock","tokens_used":0,"processing_time":818,"has_project":false} 
[2025-08-12 01:05:44] production.INFO: 文本生成任务创建成功 {"task_id":12,"user_id":13,"model_id":1,"estimated_cost":1.4} 
[2025-08-12 01:05:44] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress"} 
[2025-08-12 01:05:44] production.INFO: AI生成进度推送 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13,"progress":80,"success_count":0} 
[2025-08-12 01:05:44] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-12 01:05:44] production.INFO: AI生成进度推送 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13,"progress":100,"success_count":0} 
[2025-08-12 01:05:44] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13,"push_type":"ai_generation_completed"} 
[2025-08-12 01:05:44] production.INFO: AI生成完成推送 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13,"task_type":"text_generation","success_count":0} 
[2025-08-12 01:05:44] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1754897174_eef4JmQt","user_id":13,"context":"prompt_edit","ai_task_id":12,"cost":0} 
[2025-08-12 01:10:11] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress"} 
[2025-08-12 01:10:11] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:10:11] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress"} 
[2025-08-12 01:10:11] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:10:11] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:10:11] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"error_message":"积分不足","push_type":"ai_generation_failed"} 
[2025-08-12 01:10:11] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:10:11] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754929506_swCnWgW8","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-12 01:10:11] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"context":"测试WebSocket文本生成功能","prompt":"请写一个关于人工智能的简短介绍","error":"积分不足","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-12 01:10:11] production.ERROR: 积分不足 {"exception":"[object] (Exception(code: 0): 积分不足 at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:170)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-08-12 01:13:38] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress"} 
[2025-08-12 01:13:38] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:13:38] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress"} 
[2025-08-12 01:13:38] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:13:38] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:13:38] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"error_message":"积分不足","push_type":"ai_generation_failed"} 
[2025-08-12 01:13:38] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:13:38] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754929506_swCnWgW8","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-12 01:13:38] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"context":"测试WebSocket文本生成功能","prompt":"请写一个关于人工智能的简短介绍","error":"积分不足","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-12 01:13:38] production.ERROR: 积分不足 {"exception":"[object] (Exception(code: 0): 积分不足 at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:170)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-08-12 01:21:35] production.ERROR: Call to a member function connection() on null {"exception":"[object] (Error(code: 0): Call to a member function connection() on null at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php:1819)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1785): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1576): Illuminate\\Database\\Eloquent\\Model->getConnection()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1495): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1531): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1484): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->newQuery()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\test_websocket_progress_complete.php(23): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#8 {main}
"} 
[2025-08-12 01:22:03] production.ERROR: Call to a member function connection() on null {"exception":"[object] (Error(code: 0): Call to a member function connection() on null at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php:1819)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1785): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1576): Illuminate\\Database\\Eloquent\\Model->getConnection()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1495): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1531): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1484): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->newQuery()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\test_websocket_progress_complete.php(32): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#8 {main}
"} 
[2025-08-12 01:23:03] production.ERROR: Call to a member function connection() on null {"exception":"[object] (Error(code: 0): Call to a member function connection() on null at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php:1819)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1785): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1576): Illuminate\\Database\\Eloquent\\Model->getConnection()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1495): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1531): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1484): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->newQuery()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\test_websocket_progress.php(22): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#8 {main}
"} 
[2025-08-12 01:23:33] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress"} 
[2025-08-12 01:23:33] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:23:33] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress"} 
[2025-08-12 01:23:33] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:23:33] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:23:33] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"error_message":"积分不足","push_type":"ai_generation_failed"} 
[2025-08-12 01:23:33] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:23:34] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754929506_swCnWgW8","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-12 01:23:34] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"context":"测试WebSocket文本生成功能","prompt":"请写一个关于人工智能的简短介绍","error":"积分不足","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-12 01:23:34] production.INFO: WebSocket推送事件 - 任务最终失败 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"error_message":"积分不足","push_type":"ai_generation_failed","context":"failed_method"} 
[2025-08-12 01:23:34] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:23:34] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754929506_swCnWgW8","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-12 01:23:34] production.ERROR: 文本生成任务最终失败 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"context":"测试WebSocket文本生成功能","error":"积分不足"} 
[2025-08-12 01:23:34] production.ERROR: SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {"uuid":"8d24f12c-6450-4e08-9487-2e3d716a0377","displayName":"App\\Jobs\\ProcessTextGeneration","job":"Illuminate\\Queue\\CallQueuedHandler@call","maxTries":3,"maxExceptions":null,"failOnTimeout":false,"backoff":null,"timeout":180,"retryUntil":null,"data":{"commandName":"App\\Jobs\\ProcessTextGeneration","command":"O:30:\"App\\Jobs\\ProcessTextGeneration\":6:{s:9:\"\u0000*\u0000taskId\";s:28:\"text_gen_1754929506_swCnWgW8\";s:9:\"\u0000*\u0000userId\";i:14;s:9:\"\u0000*\u0000prompt\";s:45:\"请写一个关于人工智能的简短介绍\";s:10:\"\u0000*\u0000modelId\";i:1;s:19:\"\u0000*\u0000generationParams\";a:3:{s:10:\"max_tokens\";s:4:\"1000\";s:11:\"temperature\";s:3:\"0.7\";s:5:\"top_p\";d:0.9;}s:10:\"\u0000*\u0000context\";s:33:\"测试WebSocket文本生成功能\";}"}}, Exception: 积分不足 in D:\longtool\phpStudy_64\WWW\tool_api\php\api\app\Jobs\ProcessTextGeneration.php:170
Stack trace:
#0 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): App\Jobs\ProcessTextGeneration->handle()
#1 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#2 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#3 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#4 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#5 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\bus\Dispatcher.php(128): Illuminate\Container\Container->call(Array)
#6 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(144): Illuminate\Bus\Dispatcher->Illuminate\Bus\{closure}(Object(App\Jobs\ProcessTextGeneration))
#7 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(App\Jobs\ProcessTextGeneration))
#8 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\bus\Dispatcher.php(132): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#9 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(124): Illuminate\Bus\Dispatcher->dispatchNow(Object(App\Jobs\ProcessTextGeneration), false)
#10 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(144): Illuminate\Queue\CallQueuedHandler->Illuminate\Queue\{closure}(Object(App\Jobs\ProcessTextGeneration))
#11 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(App\Jobs\ProcessTextGeneration))
#12 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(126): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#13 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(70): Illuminate\Queue\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\Queue\Jobs\DatabaseJob), Object(App\Jobs\ProcessTextGeneration))
#14 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Jobs\Job.php(102): Illuminate\Queue\CallQueuedHandler->call(Object(Illuminate\Queue\Jobs\DatabaseJob), Array)
#15 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(439): Illuminate\Queue\Jobs\Job->fire()
#16 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(389): Illuminate\Queue\Worker->process('database', Object(Illuminate\Queue\Jobs\DatabaseJob), Object(Illuminate\Queue\WorkerOptions))
#17 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(333): Illuminate\Queue\Worker->runJob(Object(Illuminate\Queue\Jobs\DatabaseJob), 'database', Object(Illuminate\Queue\WorkerOptions))
#18 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(138): Illuminate\Queue\Worker->runNextJob('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#19 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(121): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#20 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#21 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#22 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#23 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#24 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#25 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(211): Illuminate\Container\Container->call(Array)
#26 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#27 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(181): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#28 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(1078): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#30 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#31 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\laravel\lumen-framework\src\Console\Kernel.php(160): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#32 D:\longtool\phpStudy_64\WWW\tool_api\php\api\artisan(35): Laravel\Lumen\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#33 {main}, 2025-08-12 01:23:34)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {\"uuid\":\"8d24f12c-6450-4e08-9487-2e3d716a0377\",\"displayName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":180,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"command\":\"O:30:\\\"App\\\\Jobs\\\\ProcessTextGeneration\\\":6:{s:9:\\\"\\u0000*\\u0000taskId\\\";s:28:\\\"text_gen_1754929506_swCnWgW8\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:14;s:9:\\\"\\u0000*\\u0000prompt\\\";s:45:\\\"请写一个关于人工智能的简短介绍\\\";s:10:\\\"\\u0000*\\u0000modelId\\\";i:1;s:19:\\\"\\u0000*\\u0000generationParams\\\";a:3:{s:10:\\\"max_tokens\\\";s:4:\\\"1000\\\";s:11:\\\"temperature\\\";s:3:\\\"0.7\\\";s:5:\\\"top_p\\\";d:0.9;}s:10:\\\"\\u0000*\\u0000context\\\";s:33:\\\"测试WebSocket文本生成功能\\\";}\"}}, Exception: 积分不足 in D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:170
Stack trace:
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}, 2025-08-12 01:23:34)) at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:822)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"8d24f1...', 'Exception: \\xE7\\xA7\\xAF\\xE5...')
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Exception))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(542): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Exception))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(466): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3, Object(Exception))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(443): Illuminate\\Queue\\Worker->handleJobException('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Exception))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:580)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(580): PDOStatement->execute()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into `p_...', Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"8d24f1...', 'Exception: \\xE7\\xA7\\xAF\\xE5...')
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Exception))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(542): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Exception))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(466): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3, Object(Exception))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(443): Illuminate\\Queue\\Worker->handleJobException('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Exception))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-08-12 01:25:59] production.ERROR: Call to a member function connection() on null {"exception":"[object] (Error(code: 0): Call to a member function connection() on null at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php:1819)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1785): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1576): Illuminate\\Database\\Eloquent\\Model->getConnection()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1495): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1531): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1484): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->newQuery()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('find', Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\test_websocket_complete_flow.php(20): Illuminate\\Database\\Eloquent\\Model::__callStatic('find', Array)
#8 {main}
"} 
[2025-08-12 01:26:12] production.INFO: WebSocket推送事件 - 任务最终失败 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"error_message":"App\\Jobs\\ProcessTextGeneration has been attempted too many times.","push_type":"ai_generation_failed","context":"failed_method"} 
[2025-08-12 01:26:12] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14} 
[2025-08-12 01:26:12] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754929506_swCnWgW8","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-12 01:26:12] production.ERROR: 文本生成任务最终失败 {"task_id":"text_gen_1754929506_swCnWgW8","user_id":14,"context":"测试WebSocket文本生成功能","error":"App\\Jobs\\ProcessTextGeneration has been attempted too many times."} 
[2025-08-12 01:26:12] production.ERROR: SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {"uuid":"8d24f12c-6450-4e08-9487-2e3d716a0377","displayName":"App\\Jobs\\ProcessTextGeneration","job":"Illuminate\\Queue\\CallQueuedHandler@call","maxTries":3,"maxExceptions":null,"failOnTimeout":false,"backoff":null,"timeout":180,"retryUntil":null,"data":{"commandName":"App\\Jobs\\ProcessTextGeneration","command":"O:30:\"App\\Jobs\\ProcessTextGeneration\":6:{s:9:\"\u0000*\u0000taskId\";s:28:\"text_gen_1754929506_swCnWgW8\";s:9:\"\u0000*\u0000userId\";i:14;s:9:\"\u0000*\u0000prompt\";s:45:\"请写一个关于人工智能的简短介绍\";s:10:\"\u0000*\u0000modelId\";i:1;s:19:\"\u0000*\u0000generationParams\";a:3:{s:10:\"max_tokens\";s:4:\"1000\";s:11:\"temperature\";s:3:\"0.7\";s:5:\"top_p\";d:0.9;}s:10:\"\u0000*\u0000context\";s:33:\"测试WebSocket文本生成功能\";}"}}, Illuminate\Queue\MaxAttemptsExceededException: App\Jobs\ProcessTextGeneration has been attempted too many times. in D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\MaxAttemptsExceededException.php:24
Stack trace:
#0 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(785): Illuminate\Queue\MaxAttemptsExceededException::forJob(Object(Illuminate\Queue\Jobs\DatabaseJob))
#1 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(519): Illuminate\Queue\Worker->maxAttemptsExceededException(Object(Illuminate\Queue\Jobs\DatabaseJob))
#2 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(429): Illuminate\Queue\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\Queue\Jobs\DatabaseJob), 3)
#3 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(389): Illuminate\Queue\Worker->process('database', Object(Illuminate\Queue\Jobs\DatabaseJob), Object(Illuminate\Queue\WorkerOptions))
#4 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(333): Illuminate\Queue\Worker->runJob(Object(Illuminate\Queue\Jobs\DatabaseJob), 'database', Object(Illuminate\Queue\WorkerOptions))
#5 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(138): Illuminate\Queue\Worker->runNextJob('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#6 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(121): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#7 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#8 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#9 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#10 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#11 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#12 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(211): Illuminate\Container\Container->call(Array)
#13 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#14 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(181): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#15 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(1078): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#16 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#17 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#18 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\laravel\lumen-framework\src\Console\Kernel.php(160): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#19 D:\longtool\phpStudy_64\WWW\tool_api\php\api\artisan(35): Laravel\Lumen\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#20 {main}, 2025-08-12 01:26:12)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {\"uuid\":\"8d24f12c-6450-4e08-9487-2e3d716a0377\",\"displayName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":180,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"command\":\"O:30:\\\"App\\\\Jobs\\\\ProcessTextGeneration\\\":6:{s:9:\\\"\\u0000*\\u0000taskId\\\";s:28:\\\"text_gen_1754929506_swCnWgW8\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:14;s:9:\\\"\\u0000*\\u0000prompt\\\";s:45:\\\"请写一个关于人工智能的简短介绍\\\";s:10:\\\"\\u0000*\\u0000modelId\\\";i:1;s:19:\\\"\\u0000*\\u0000generationParams\\\";a:3:{s:10:\\\"max_tokens\\\";s:4:\\\"1000\\\";s:11:\\\"temperature\\\";s:3:\\\"0.7\\\";s:5:\\\"top_p\\\";d:0.9;}s:10:\\\"\\u0000*\\u0000context\\\";s:33:\\\"测试WebSocket文本生成功能\\\";}\"}}, Illuminate\\Queue\\MaxAttemptsExceededException: App\\Jobs\\ProcessTextGeneration has been attempted too many times. in D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\MaxAttemptsExceededException.php:24
Stack trace:
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(785): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(519): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(429): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}, 2025-08-12 01:26:12)) at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:822)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"8d24f1...', 'Illuminate\\\\Queu...')
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Illuminate\\Queue\\MaxAttemptsExceededException))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(519): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\MaxAttemptsExceededException))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(429): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:580)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(580): PDOStatement->execute()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into `p_...', Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"8d24f1...', 'Illuminate\\\\Queu...')
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Illuminate\\Queue\\MaxAttemptsExceededException))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(519): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\MaxAttemptsExceededException))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(429): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3)
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
[2025-08-12 01:44:05] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress"} 
[2025-08-12 01:44:05] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14} 
[2025-08-12 01:44:05] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress"} 
[2025-08-12 01:44:05] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14} 
[2025-08-12 01:44:05] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"websocket_session_id":null} 
[2025-08-12 01:44:05] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"error_message":"积分不足","push_type":"ai_generation_failed"} 
[2025-08-12 01:44:05] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14} 
[2025-08-12 01:44:06] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754930935_fYYj4hVg","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-12 01:44:06] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"context":"测试WebSocket文本生成功能","prompt":"请写一个关于人工智能的简短介绍","error":"积分不足","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-12 01:44:06] production.ERROR: 积分不足 {"exception":"[object] (Exception(code: 0): 积分不足 at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:178)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-08-12 01:49:09] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress"} 
[2025-08-12 01:49:09] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14} 
[2025-08-12 01:49:09] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress"} 
[2025-08-12 01:49:09] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14} 
[2025-08-12 01:49:09] production.WARNING: 未找到活跃的WebSocket会话或business_type为空 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"websocket_session_id":null} 
[2025-08-12 01:49:09] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"error_message":"积分不足","push_type":"ai_generation_failed"} 
[2025-08-12 01:49:09] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14} 
[2025-08-12 01:49:09] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754930935_fYYj4hVg","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-12 01:49:09] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"context":"测试WebSocket文本生成功能","prompt":"请写一个关于人工智能的简短介绍","error":"积分不足","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-12 01:49:09] production.ERROR: 积分不足 {"exception":"[object] (Exception(code: 0): 积分不足 at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:178)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-08-12 01:51:05] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress"} 
[2025-08-12 01:51:05] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14} 
[2025-08-12 01:51:05] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress"} 
[2025-08-12 01:51:05] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14} 
[2025-08-12 01:51:05] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"session_id":"ws_RoVwuPcrO7jQGwFtpW44G5s2QKdWgr5v","business_type":"text_generation"} 
[2025-08-12 01:51:05] production.INFO: WebSocket推送事件 - 任务失败 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"error_message":"积分不足","push_type":"ai_generation_failed"} 
[2025-08-12 01:51:05] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14} 
[2025-08-12 01:51:05] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754930935_fYYj4hVg","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-12 01:51:05] production.ERROR: 文本生成任务失败 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"context":"测试WebSocket文本生成功能","prompt":"请写一个关于人工智能的简短介绍","error":"积分不足","trace":"#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"} 
[2025-08-12 01:51:05] production.INFO: WebSocket推送事件 - 任务最终失败 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"error_message":"积分不足","push_type":"ai_generation_failed","context":"failed_method"} 
[2025-08-12 01:51:05] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14} 
[2025-08-12 01:51:05] production.ERROR: 发布文本生成失败事件异常 {"task_id":"text_gen_1754930935_fYYj4hVg","error":"cURL error 60: SSL certificate problem: unable to get local issuer certificate (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.tiptop.cn/py-api/events/publish"} 
[2025-08-12 01:51:05] production.ERROR: 文本生成任务最终失败 {"task_id":"text_gen_1754930935_fYYj4hVg","user_id":14,"context":"测试WebSocket文本生成功能","error":"积分不足"} 
[2025-08-12 01:51:05] production.ERROR: SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {"uuid":"b06d44fc-b187-4f61-b3d6-ad3adff16d84","displayName":"App\\Jobs\\ProcessTextGeneration","job":"Illuminate\\Queue\\CallQueuedHandler@call","maxTries":3,"maxExceptions":null,"failOnTimeout":false,"backoff":null,"timeout":180,"retryUntil":null,"data":{"commandName":"App\\Jobs\\ProcessTextGeneration","command":"O:30:\"App\\Jobs\\ProcessTextGeneration\":6:{s:9:\"\u0000*\u0000taskId\";s:28:\"text_gen_1754930935_fYYj4hVg\";s:9:\"\u0000*\u0000userId\";i:14;s:9:\"\u0000*\u0000prompt\";s:45:\"请写一个关于人工智能的简短介绍\";s:10:\"\u0000*\u0000modelId\";i:1;s:19:\"\u0000*\u0000generationParams\";a:3:{s:10:\"max_tokens\";s:4:\"1000\";s:11:\"temperature\";s:3:\"0.7\";s:5:\"top_p\";d:0.9;}s:10:\"\u0000*\u0000context\";s:33:\"测试WebSocket文本生成功能\";}"}}, Exception: 积分不足 in D:\longtool\phpStudy_64\WWW\tool_api\php\api\app\Jobs\ProcessTextGeneration.php:178
Stack trace:
#0 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): App\Jobs\ProcessTextGeneration->handle()
#1 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#2 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#3 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#4 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#5 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\bus\Dispatcher.php(128): Illuminate\Container\Container->call(Array)
#6 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(144): Illuminate\Bus\Dispatcher->Illuminate\Bus\{closure}(Object(App\Jobs\ProcessTextGeneration))
#7 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(App\Jobs\ProcessTextGeneration))
#8 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\bus\Dispatcher.php(132): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#9 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(124): Illuminate\Bus\Dispatcher->dispatchNow(Object(App\Jobs\ProcessTextGeneration), false)
#10 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(144): Illuminate\Queue\CallQueuedHandler->Illuminate\Queue\{closure}(Object(App\Jobs\ProcessTextGeneration))
#11 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(App\Jobs\ProcessTextGeneration))
#12 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(126): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#13 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\CallQueuedHandler.php(70): Illuminate\Queue\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\Queue\Jobs\DatabaseJob), Object(App\Jobs\ProcessTextGeneration))
#14 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Jobs\Job.php(102): Illuminate\Queue\CallQueuedHandler->call(Object(Illuminate\Queue\Jobs\DatabaseJob), Array)
#15 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(439): Illuminate\Queue\Jobs\Job->fire()
#16 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(389): Illuminate\Queue\Worker->process('database', Object(Illuminate\Queue\Jobs\DatabaseJob), Object(Illuminate\Queue\WorkerOptions))
#17 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Worker.php(333): Illuminate\Queue\Worker->runJob(Object(Illuminate\Queue\Jobs\DatabaseJob), 'database', Object(Illuminate\Queue\WorkerOptions))
#18 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(138): Illuminate\Queue\Worker->runNextJob('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#19 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\queue\Console\WorkCommand.php(121): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#20 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#21 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#22 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#23 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(Laravel\Lumen\Application), Array, Object(Closure))
#24 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Laravel\Lumen\Application), Array, Array, NULL)
#25 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(211): Illuminate\Container\Container->call(Array)
#26 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#27 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\illuminate\console\Command.php(181): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#28 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(1078): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#30 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#31 D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\laravel\lumen-framework\src\Console\Kernel.php(160): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#32 D:\longtool\phpStudy_64\WWW\tool_api\php\api\artisan(35): Laravel\Lumen\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#33 {main}, 2025-08-12 01:51:05)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value (Connection: mysql, SQL: insert into `p_failed_jobs` (`connection`, `queue`, `payload`, `exception`, `failed_at`) values (database, default, {\"uuid\":\"b06d44fc-b187-4f61-b3d6-ad3adff16d84\",\"displayName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":180,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\ProcessTextGeneration\",\"command\":\"O:30:\\\"App\\\\Jobs\\\\ProcessTextGeneration\\\":6:{s:9:\\\"\\u0000*\\u0000taskId\\\";s:28:\\\"text_gen_1754930935_fYYj4hVg\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:14;s:9:\\\"\\u0000*\\u0000prompt\\\";s:45:\\\"请写一个关于人工智能的简短介绍\\\";s:10:\\\"\\u0000*\\u0000modelId\\\";i:1;s:19:\\\"\\u0000*\\u0000generationParams\\\";a:3:{s:10:\\\"max_tokens\\\";s:4:\\\"1000\\\";s:11:\\\"temperature\\\";s:3:\\\"0.7\\\";s:5:\\\"top_p\\\";d:0.9;}s:10:\\\"\\u0000*\\u0000context\\\";s:33:\\\"测试WebSocket文本生成功能\\\";}\"}}, Exception: 积分不足 in D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php:178
Stack trace:
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): App\\Jobs\\ProcessTextGeneration->handle()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(128): Illuminate\\Container\\Container->call(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\bus\\Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessTextGeneration), false)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(144): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessTextGeneration))
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessTextGeneration))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}, 2025-08-12 01:51:05)) at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:822)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"b06d44...', 'Exception: \\xE7\\xA7\\xAF\\xE5...')
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Exception))
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(542): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Exception))
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(466): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3, Object(Exception))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(443): Illuminate\\Queue\\Worker->handleJobException('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Exception))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1364 Field 'uuid' doesn't have a default value at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php:580)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(580): PDOStatement->execute()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into `p_...', Array)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `p_...', Array, Object(Closure))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into `p_...', Array, Object(Closure))
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(533): Illuminate\\Database\\Connection->statement('insert into `p_...', Array)
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into `p_...', Array)
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Query\\Builder.php(3387): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `p_...', Array, NULL)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Failed\\DatabaseFailedJobProvider.php(63): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#8 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(280): Illuminate\\Queue\\Failed\\DatabaseFailedJobProvider->log('database', 'default', '{\"uuid\":\"b06d44...', 'Exception: \\xE7\\xA7\\xAF\\xE5...')
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(186): Illuminate\\Queue\\Console\\WorkCommand->logFailedJob(Object(Illuminate\\Queue\\Events\\JobFailed))
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(458): Illuminate\\Queue\\Console\\WorkCommand->Illuminate\\Queue\\Console\\{closure}(Object(Illuminate\\Queue\\Events\\JobFailed))
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('Illuminate\\\\Queu...', Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('Illuminate\\\\Queu...', Array, false)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Jobs\\Job.php(218): Illuminate\\Events\\Dispatcher->dispatch('Illuminate\\\\Queu...')
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(596): Illuminate\\Queue\\Jobs\\Job->fail(Object(Exception))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(542): Illuminate\\Queue\\Worker->failJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Exception))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(466): Illuminate\\Queue\\Worker->markJobAsFailedIfWillExceedMaxAttempts('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 3, Object(Exception))
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(443): Illuminate\\Queue\\Worker->handleJobException('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions), Object(Exception))
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(389): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Worker.php(333): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(138): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\queue\\Console\\WorkCommand.php(121): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-08-12 02:09:17] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"text","mapped_task_type":"text_generation","user_id":14,"session_id":"ws_vCvD9OyeM4tLL8WUdQ1qLtLkTJBuIqWw"} 
[2025-08-12 02:09:17] production.INFO: WebSocket连接认证成功 {"session_id":"ws_vCvD9OyeM4tLL8WUdQ1qLtLkTJBuIqWw","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-12 02:11:26] production.INFO: 文本生成任务创建成功 {"task_id":"text_gen_1754935886_mvc5gqmQ","user_id":14,"context":"prompt_edit","model_id":null,"project_id":null} 
