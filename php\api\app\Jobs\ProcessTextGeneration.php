<?php

namespace App\Jobs;

use App\Services\PyApi\AiGenerationService;
use App\Services\PyApi\WebSocketEventService;
use App\Models\WebSocketSession;
use App\Enums\ApiCodeEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

/**
 * 异步处理文本生成任务
 * 实现图表中的完整 WebSocket 进度推送流程
 */
class ProcessTextGeneration implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    protected $taskId;
    protected $userId;
    protected $prompt;
    protected $modelId;
    protected $projectId;
    protected $generationParams;
    protected $context;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 180; // 3分钟

    /**
     * 最大重试次数
     */
    public $tries = 3;

    public function __construct(
        string $taskId,
        int $userId,
        string $prompt,
        ?int $modelId = null,
        ?int $projectId = null,
        array $generationParams = [],
        string $context = 'prompt_edit'
    ) {
        $this->taskId = $taskId;
        $this->userId = $userId;
        $this->prompt = $prompt;
        $this->modelId = $modelId;
        $this->projectId = $projectId;
        $this->generationParams = $generationParams;
        $this->context = $context;
    }

    /**
     * 执行任务
     */
    public function handle()
    {
        $webSocketEventService = app(WebSocketEventService::class);
        $aiGenerationService = app(AiGenerationService::class);

        try {
            DB::beginTransaction();

            // 推送任务开始进度
            Log::info('WebSocket推送进度 - 任务开始', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'progress' => 10,
                'message' => '开始文本生成任务',
                'push_type' => 'ai_generation_progress'
            ]);
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                10,
                "开始文本生成任务"
            );

            // 推送进度更新
            Log::info('WebSocket推送进度 - 连接AI平台', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'progress' => 30,
                'message' => '连接AI平台',
                'push_type' => 'ai_generation_progress'
            ]);
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                30,
                "连接AI平台"
            );

            // 从活跃的WebSocket会话中获取business_type
            $businessType = $this->getBusinessTypeFromWebSocketSession();

            // 调用AiGenerationService生成文本
            $result = $aiGenerationService->generateText(
                $this->userId,
                $this->prompt,
                $this->modelId,
                $this->projectId,
                $this->generationParams,
                $businessType,
                $this->taskId  // 传递外部任务ID
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 生成成功
                Log::info('WebSocket推送进度 - 保存文本数据', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'progress' => 80,
                    'message' => '保存文本数据',
                    'push_type' => 'ai_generation_progress'
                ]);
                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    80,
                    "保存文本数据"
                );

                // 推送完成进度
                Log::info('WebSocket推送进度 - 文本生成完成', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'progress' => 100,
                    'message' => '文本生成完成',
                    'push_type' => 'ai_generation_progress'
                ]);
                $webSocketEventService->pushAiGenerationProgress(
                    $this->taskId,
                    $this->userId,
                    100,
                    "文本生成完成"
                );

                // 推送任务完成事件
                Log::info('WebSocket推送事件 - 任务完成', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'push_type' => 'ai_generation_completed'
                ]);
                $webSocketEventService->pushAiGenerationCompleted(
                    $this->taskId,
                    $this->userId
                );

                DB::commit();

                Log::info('文本生成任务完成', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'context' => $this->context,
                    'ai_task_id' => $result['data']['task_id'] ?? null,
                    'cost' => $result['data']['cost'] ?? 0
                ]);

            } else {
                // 生成失败
                throw new \Exception($result['message']);
            }

        } catch (\Exception $e) {
            DB::rollBack();

            // 推送任务失败事件
            Log::info('WebSocket推送事件 - 任务失败', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'error_message' => $e->getMessage(),
                'push_type' => 'ai_generation_failed'
            ]);
            $webSocketEventService->pushAiGenerationFailed(
                $this->taskId,
                $this->userId,
                $e->getMessage()
            );

            // 发布失败事件到事件总线
            $this->publishFailureEvent($e->getMessage());

            Log::error('文本生成任务失败', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'context' => $this->context,
                'prompt' => substr($this->prompt, 0, 100),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 任务失败处理
     */
    public function failed(\Throwable $exception)
    {
        $webSocketEventService = app(WebSocketEventService::class);

        // 推送任务失败事件
        Log::info('WebSocket推送事件 - 任务最终失败', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'error_message' => $exception->getMessage(),
            'push_type' => 'ai_generation_failed',
            'context' => 'failed_method'
        ]);
        $webSocketEventService->pushAiGenerationFailed(
            $this->taskId,
            $this->userId,
            $exception->getMessage()
        );

        // 发布最终失败事件
        $this->publishFailureEvent($exception->getMessage());

        Log::error('文本生成任务最终失败', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'context' => $this->context,
            'error' => $exception->getMessage()
        ]);
    }

    /**
     * 从活跃的WebSocket会话中获取business_type
     */
    private function getBusinessTypeFromWebSocketSession(): ?string
    {
        try {
            // 查询用户的活跃WebSocket会话
            $session = WebSocketSession::byUser($this->userId)
                ->active()
                ->orderBy('connected_at', 'desc')
                ->first();

            if ($session && !empty($session->business_type)) {

                Log::info('从WebSocket会话获取business_type', [
                    'task_id' => $this->taskId,
                    'user_id' => $this->userId,
                    'session_id' => $session->session_id,
                    'business_type' => $session->business_type
                ]);

                return $session->business_type;
            }

            Log::warning('未找到活跃的WebSocket会话或business_type为空', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('获取WebSocket会话business_type失败', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 发布失败事件到事件总线
     */
    private function publishFailureEvent(string $errorMessage): void
    {
        try {
            // 调用事件发布API
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('app.internal_api_token', 'internal_service_token'),
                'Content-Type' => 'application/json'
            ])->post(config('app.url') . '/py-api/events/publish', [
                'event_type' => 'text_generation_failed',
                'business_id' => $this->taskId,
                'user_id' => $this->userId,
                'error_details' => [
                    'error_message' => $errorMessage,
                    'task_id' => $this->taskId,
                    'context' => $this->context,
                    'prompt' => substr($this->prompt, 0, 200)
                ],
                'metadata' => [
                    'prompt' => $this->prompt,
                    'generation_params' => $this->generationParams,
                    'model_id' => $this->modelId,
                    'project_id' => $this->projectId,
                    'context' => $this->context,
                    'failed_at' => \Carbon\Carbon::now()->toISOString()
                ]
            ]);

            if ($response->successful()) {
                Log::info('文本生成失败事件发布成功', [
                    'task_id' => $this->taskId,
                    'event_response' => $response->json()
                ]);
            } else {
                Log::warning('文本生成失败事件发布失败', [
                    'task_id' => $this->taskId,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('发布文本生成失败事件异常', [
                'task_id' => $this->taskId,
                'error' => $e->getMessage()
            ]);
        }
    }
}
